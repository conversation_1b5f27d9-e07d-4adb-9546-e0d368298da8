#!/usr/bin/env python3
"""
测试多工具调用功能

这个脚本用于测试circular_thinker代理是否支持在一个turn中调用多个工具。
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the modules
sys.path.append(str(Path(__file__).parent))

from utils.llm_client import get_client
from tools.agent import Agent
from utils.workspace_manager import WorkspaceManager
from rich.console import Console
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_multi_tool_calls():
    """测试多工具调用功能"""
    
    # 设置环境变量（如果需要）
    if not os.getenv("OPENAI_API_KEY"):
        os.environ["OPENAI_API_KEY"] = "123"
    if not os.getenv("OPENAI_BASE_URL"):
        os.environ["OPENAI_BASE_URL"] = "http://100.80.20.5:4000/v1"
    
    # 创建必要的组件
    console = Console()
    logger = setup_logging()
    workspace_manager = WorkspaceManager(Path("."))
    
    # 创建LLM客户端
    client = get_client(
        "openai-direct",
        model_name="deepseek-ai/DeepSeek-R1-0528",
        debug=True  # 启用debug模式查看多工具调用
    )
    
    # 创建代理
    agent = Agent(
        client=client,
        workspace_manager=workspace_manager,
        console=console,
        logger_for_agent_logs=logger,
        temperature=0.7,
        max_turns=3  # 限制turn数量以便测试
    )
    
    # 测试问题：要求代理执行多个操作
    test_problem = """
    请执行以下操作：
    1. 首先使用sequential_thinking工具思考如何创建一个简单的Python脚本
    2. 然后使用bash工具查看当前目录的内容
    3. 最后使用str_replace_editor工具创建一个简单的hello.py文件
    
    请在一个回合中尽可能多地调用工具来完成这些任务。
    """
    
    print("=" * 80)
    print("测试多工具调用功能")
    print("=" * 80)
    print(f"测试问题: {test_problem}")
    print("=" * 80)
    
    try:
        # 运行代理
        result = agent.run_impl({"instruction": test_problem})
        
        print("\n" + "=" * 80)
        print("测试结果:")
        print("=" * 80)
        print(f"工具输出: {result.tool_output}")
        print(f"结果消息: {result.tool_result_message}")
        
        if "completed" in result.tool_result_message.lower():
            print("\n✅ 测试成功：代理完成了任务")
        else:
            print("\n⚠️ 测试部分成功：代理执行了部分任务")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_multi_tool_calls()
