# Deep Research UI - Docker Deployment Guide

This guide explains how to deploy the Deep Research UI using Docker containers.

## Quick Start

### Prerequisites

- Docker installed and running
- Docker Compose (optional, but recommended)
- API keys for the services you want to use

### 1. Environment Setup

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit the `.env` file with your actual API keys:

```bash
# Required: Set your API keys
EXA_API_KEY=your-actual-exa-api-key
OPENAI_API_KEY=your-actual-openai-api-key

# Optional: Adjust other settings as needed
OPENAI_API_BASE_URL=http://***********:4000
OPENAI_MODEL_TOOL=QwQ-32B
OPENAI_MODEL_SUMMARY=DeepSeek-V3-0324
```

### 2. Using Docker Compose (Recommended)

The easiest way to run the application:

```bash
# Start the application
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the application
docker-compose down
```

### 3. Using the Build Script

We provide a convenient build script for common Docker operations:

```bash
# Build the Docker image
./docker-build.sh build

# Run the container
./docker-build.sh run

# View logs
./docker-build.sh logs

# Stop the container
./docker-build.sh stop

# Clean up (remove container and image)
./docker-build.sh clean
```

### 4. Manual Docker Commands

If you prefer to use Docker commands directly:

```bash
# Build the image
docker build -t deep-research-ui .

# Run the container
docker run -d \
  --name deep-research-ui \
  -p 3000:3000 \
  --env-file .env \
  deep-research-ui

# View logs
docker logs -f deep-research-ui

# Stop and remove
docker stop deep-research-ui
docker rm deep-research-ui
```

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `USE_OPENAI` | Use OpenAI models (true) or Google models (false) | `true` | No |
| `OPENAI_API_KEY` | OpenAI API key | - | Yes (if USE_OPENAI=true) |
| `OPENAI_API_BASE_URL` | OpenAI API base URL | `http://***********:4000` | No |
| `OPENAI_MODEL_TOOL` | Model for search and analysis | `QwQ-32B` | No |
| `OPENAI_MODEL_SUMMARY` | Model for report generation | `DeepSeek-V3-0324` | No |
| `GOOGLE_GENERATIVE_AI_API_KEY` | Google AI API key | - | Yes (if USE_OPENAI=false) |
| `EXA_API_KEY` | Exa search API key | - | Yes |
| `RAG_API_BASE_URL` | RAG API endpoint | `http://localhost:8000` | No |
| `RAG_API_KEY` | RAG API key | - | No |
| `PORT` | Server port | `3000` | No |
| `HOST` | Server host | `0.0.0.0` | No |

### Port Configuration

By default, the application runs on port 3000. You can change this:

```bash
# Using docker-compose
PORT=8080 docker-compose up -d

# Using the build script
./docker-build.sh run -p 8080

# Using Docker directly
docker run -d -p 8080:3000 --env-file .env deep-research-ui
```

## Build Script Commands

The `docker-build.sh` script provides several convenient commands:

```bash
# Build the Docker image
./docker-build.sh build

# Run the container
./docker-build.sh run [options]

# Stop the container
./docker-build.sh stop

# Restart the container
./docker-build.sh restart

# View container logs
./docker-build.sh logs

# Open a shell in the running container
./docker-build.sh shell

# Clean up (remove container and image)
./docker-build.sh clean

# Docker Compose operations
./docker-build.sh compose up
./docker-build.sh compose down
./docker-build.sh compose logs
```

### Build Script Options

```bash
# Custom image tag
./docker-build.sh build -t v1.0.0

# Custom port
./docker-build.sh run -p 8080

# Custom container name
./docker-build.sh run -n my-research-ui

# Show help
./docker-build.sh -h
```

## Accessing the Application

Once the container is running, you can access the Deep Research UI at:

- **Local access**: http://localhost:3000
- **Network access**: http://your-server-ip:3000

## Troubleshooting

### Container Won't Start

1. Check if the port is already in use:
   ```bash
   lsof -i :3000
   ```

2. Check container logs:
   ```bash
   docker logs deep-research-ui
   ```

3. Verify environment variables:
   ```bash
   docker exec deep-research-ui env | grep -E "(OPENAI|EXA|RAG)"
   ```

### API Key Issues

1. Ensure your `.env` file has the correct API keys
2. Check that the API endpoints are accessible from the container
3. Verify API key permissions and quotas

### Performance Issues

1. Check container resource usage:
   ```bash
   docker stats deep-research-ui
   ```

2. Increase container memory if needed:
   ```bash
   docker run -d --memory=2g --name deep-research-ui ...
   ```

### Network Issues

1. If using custom networks, ensure proper connectivity:
   ```bash
   docker network ls
   docker network inspect deep-research-network
   ```

2. For external API access, check firewall settings

## Health Checks

The container includes a built-in health check that verifies the application is responding:

```bash
# Check container health
docker ps --format "table {{.Names}}\t{{.Status}}"

# View health check logs
docker inspect deep-research-ui | grep -A 10 "Health"
```

## Logs and Monitoring

### Viewing Logs

```bash
# Real-time logs
docker logs -f deep-research-ui

# Last 100 lines
docker logs --tail 100 deep-research-ui

# Logs with timestamps
docker logs -t deep-research-ui
```

### Log Persistence

To persist logs outside the container, mount a volume:

```bash
docker run -d \
  --name deep-research-ui \
  -p 3000:3000 \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  deep-research-ui
```

## Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Network**: Use Docker networks to isolate containers
3. **Updates**: Regularly update the base image and dependencies
4. **Firewall**: Restrict access to necessary ports only

## Production Deployment

For production deployment, consider:

1. **Reverse Proxy**: Use nginx or traefik for SSL termination
2. **Orchestration**: Use Docker Swarm or Kubernetes for scaling
3. **Monitoring**: Implement proper logging and monitoring
4. **Backup**: Regular backup of configuration and data
5. **Security**: Regular security updates and vulnerability scanning

## Support

If you encounter issues:

1. Check the logs first
2. Verify your environment configuration
3. Ensure all required API keys are valid
4. Check network connectivity to external APIs
