import 'dotenv/config';

// Import the searchKnowledgebase function - we need to export it from main.ts first
// For now, let's create a standalone test that replicates the function

const RAG_API_BASE_URL = process.env.RAG_API_BASE_URL || 'http://localhost:8000';
const RAG_API_KEY = process.env.RAG_API_KEY;

type SearchResult = {
  title: string;
  url: string;
  content: string;
};

const testSearchKnowledgebase = async (query: string): Promise<SearchResult[]> => {
  try {
    console.log(`Testing knowledgebase search for: "${query}"`);
    
    // Prepare the request body according to the OpenAPI QueryRequest schema
    const requestBody = {
      query: query,
      mode: 'hybrid', // Default mode as specified in the schema
      // Configure token limits for better context retrieval
      max_token_for_text_unit: 131072, // 128k tokens
      max_token_for_global_context: 131072, // 128k tokens
      max_token_for_local_context: 131072, // 128k tokens
      // Custom user prompt to preserve details and avoid over-summarization
      user_prompt: `Please provide a comprehensive and detailed response to the query. Do not summarize or condense the information - include all relevant details, technical specifications, examples, and context. The response will be further processed by another system, so preserving complete information is more important than brevity. Include specific steps, code examples, configuration details, and any nuances that might be relevant to the topic.`,
    };

    // Prepare headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add API key to query parameters if available
    const url = new URL('/query', RAG_API_BASE_URL);
    if (RAG_API_KEY) {
      url.searchParams.append('api_key_header_value', RAG_API_KEY);
    }

    console.log(`Making request to: ${url.toString()}`);
    console.log(`Request body:`, JSON.stringify(requestBody, null, 2));

    // Make the API call
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody),
    });

    console.log(`Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`RAG API error response:`, errorText);
      throw new Error(`RAG API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`Response data:`, JSON.stringify(data, null, 2));
    
    // Validate the response according to QueryResponse schema
    if (!data || typeof data.response !== 'string') {
      console.warn('Invalid response format from RAG API:', data);
      return [];
    }

    // Convert the RAG response to SearchResult format to match the expected interface
    const searchResult: SearchResult = {
      title: `Knowledge Base Result for: ${query}`,
      url: `${RAG_API_BASE_URL}/query`,
      content: data.response,
    };

    console.log(`Successfully found knowledgebase result for query: "${query}"`);
    return [searchResult];
    
  } catch (error) {
    console.error('Error searching the knowledgebase:', error);
    return [];
  }
};

// Test cases
const testQueries = [
  'What is machine learning?',
  'How to install Ubuntu?',
  'Explain REST APIs',
  'Docker containers overview',
];

const runTests = async () => {
  console.log('=== Testing searchKnowledgebase function ===');
  console.log(`RAG API Base URL: ${RAG_API_BASE_URL}`);
  console.log(`RAG API Key configured: ${RAG_API_KEY ? 'Yes' : 'No'}`);
  console.log('');

  for (const query of testQueries) {
    console.log(`\n--- Testing query: "${query}" ---`);
    try {
      const results = await testSearchKnowledgebase(query);
      
      if (results.length > 0) {
        console.log(`✅ Success! Found ${results.length} result(s)`);
        console.log(`Title: ${results[0].title}`);
        console.log(`Content preview: ${results[0].content.substring(0, 200)}...`);
      } else {
        console.log(`❌ No results found`);
      }
    } catch (error) {
      console.error(`❌ Error testing query "${query}":`, error);
    }
    
    // Add a small delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n=== Test completed ===');
};

// Run the tests
runTests().catch(error => {
  console.error('Error in test script:', error);
  process.exit(1);
});
