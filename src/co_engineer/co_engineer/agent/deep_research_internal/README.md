# Deep Research Multi-Agent System

A state-of-the-art collaborative multi-agent research system that performs exhaustive web searches and generates expert-level comprehensive reports on any topic. This advanced system orchestrates a team of specialized intelligent agents working in concert, leveraging Google's Gemini AI or OpenAI models, Vercel AI SDK, and the Exa search library to conduct recursive, multi-level research with dynamic follow-up questions.

The system's distributed agent architecture enables unprecedented research depth and breadth, with each specialized agent focusing on a specific aspect of the research process while maintaining shared context. This approach allows for parallel processing, sophisticated reasoning, and the ability to explore complex topics with human-like curiosity and thoroughness.

## How It Works

The enhanced multi-agent system coordinates a sophisticated research workflow through specialized agents with intelligent adaptation:

1. **Query Generation**: The query formulation agent generates multiple strategic search queries based on the initial research topic
2. **Adaptive Search Execution**: The unified search & evaluation agent performs:
   - **Knowledgebase Search**: Always queries internal RAG (Retrieval-Augmented Generation) systems for relevant documentation and knowledge
   - **Web Search**: Conditionally retrieves high-quality content using the Exa search library (can be enabled/disabled)
3. **Integrated Result Evaluation**: Real-time assessment of search results for relevance, authority, and uniqueness during the search process
4. **Learning Extraction**: The insight extraction agent identifies key concepts, facts, and implications from relevant sources
5. **Follow-up Questions**: Based on extracted insights, the system generates intelligent follow-up questions to explore knowledge gaps
6. **Recursive Exploration**: The orchestration agent manages recursive exploration of follow-up questions at decreasing depth levels
7. **Intelligent Agent Selection**: The system analyzes the research query to determine the most appropriate report format:
   - **How-to questions** → Detailed step-by-step procedural guides
   - **General questions** → Comprehensive analytical research reports
8. **Adaptive Report Generation**: The selected specialized agent synthesizes all research data into the most appropriate format

### Agent Collaboration Flow

The multi-agent system's collaborative research process follows this enhanced coordination pattern with intelligent agent selection and adaptive search capabilities:

```mermaid
graph TD
    A[main] --> B[deepResearch]
    B --> C[generateSearchQueries]
    B --> D[searchAndProcess]
    D --> E[searchWeb - conditional]
    D --> E2[searchKnowledgebase - always enabled]
    D --> F[evaluate - integrated]
    B --> G[generateLearnings]
    B --> B1[deepResearch - recursive]
    A --> H[selectAgentAndGenerateReport]
    H --> I[isHowToQuestion]
    I -->|how-to query| J[generateStepByStepGuide]
    I -->|general query| K[generateReport]

    C -->|returns queries| B
    E -->|returns web search results| D
    E2 -->|returns knowledgebase results| D
    F -->|evaluates relevance inline| D
    D -->|returns relevant results| B
    G -->|extracts learnings & follow-up questions| B
    B -->|accumulated research| A
    H -->|selects appropriate agent| A
    J -->|step-by-step guide| A
    K -->|research report| A
```

**Enhanced Agent Collaboration Description:**

- **Main Coordinator**: Initiates the research process and manages intelligent report generation through agent selection
- **Orchestration Agent** (`deepResearch`): Coordinates the entire multi-agent workflow recursively with configurable search options
- **Query Formulation Agent** (`generateSearchQueries`): Creates strategic search queries from the research prompt
- **Unified Search & Evaluation Agent** (`searchAndProcess`):
  - Manages both web search and knowledgebase search execution
  - Integrates real-time relevance evaluation within the search process
  - Supports conditional web search (can be enabled/disabled)
  - Always includes knowledgebase search for comprehensive coverage
- **Web Search Agent** (`searchWeb`): Interfaces with the Exa library to retrieve high-quality web content (conditional)
- **Knowledgebase Search Agent** (`searchKnowledgebase`): Queries internal RAG systems for relevant documentation and knowledge (always enabled)
- **Insight Extraction Agent** (`generateLearnings`): Identifies key concepts and generates follow-up questions
- **Agent Selection Coordinator** (`selectAgentAndGenerateReport`): Intelligently chooses between different report generation strategies
- **Query Analysis Agent** (`isHowToQuestion`): Determines if the research query requires procedural instructions or general information
- **Step-by-Step Guide Agent** (`generateStepByStepGuide`): Generates detailed procedural guides for how-to questions
- **Research Report Agent** (`generateReport`): Synthesizes all collected research into comprehensive analytical reports

## Architecture

The agent is built using:
- **AI Models**:
  - **Google Gemini AI**: Provides language model capabilities (gemini-2.0-flash-001)
  - **OpenAI-compatible API**: Supports both official OpenAI models and compatible alternatives:
    - Default tool model: QwQ-32B (for search and analysis)
    - Default summary model: DeepSeek-V3-0324 (for report generation)
- **Vercel AI SDK**: Provides the framework for AI tool integration and model switching
- **Exa Search Library**: Enables high-quality web search with content extraction
- **TypeScript**: The agent is implemented in TypeScript for type safety and modern JavaScript features

### Data Flow

The following diagram illustrates how data flows through the enhanced research process with intelligent agent selection:

```mermaid
flowchart TD
    subgraph Input
        A[Research Topic]
        A1[enableWebSearch Flag]
    end

    subgraph Research Process
        B[deepResearch]
        C[generateSearchQueries]
        D[searchAndProcess]
        E[searchWeb - conditional]
        E2[searchKnowledgebase - always]
        F[generateLearnings]
        G[Recursive deepResearch]
    end

    subgraph Data Storage
        H[(accumulatedResearch)]
        H1[queries]
        H2[searchResults - web & knowledgebase]
        H3[learnings]
        H4[completedQueries]
    end

    subgraph Intelligent Output Selection
        I[selectAgentAndGenerateReport]
        I1[isHowToQuestion]
        I2{Query Type Analysis}
        J1[generateStepByStepGuide]
        J2[generateReport]
    end

    subgraph Output
        K1[Step-by-Step Guide]
        K2[Research Report]
    end

    A --> B
    A1 --> D
    B --> C
    C --> H1
    H1 --> D
    D --> E
    D --> E2
    E --> D
    E2 --> D
    D --> H2
    H2 --> F
    F --> H3
    H3 --> G
    G --> B
    H --> I
    I --> I1
    I1 --> I2
    I2 -->|How-to Query| J1
    I2 -->|General Query| J2
    J1 --> K1
    J2 --> K2

    %% Data relationships
    H -.-> H1
    H -.-> H2
    H -.-> H3
    H -.-> H4
```

**Enhanced Data Flow Features:**

- **Adaptive Search**: The system supports both web search (conditional) and knowledgebase search (always enabled)
- **Unified Result Processing**: Both web and knowledgebase results flow through the same evaluation and learning extraction pipeline
- **Intelligent Output Selection**: The system analyzes the research query to automatically select the most appropriate report format
- **Dual Output Modes**: Generates either step-by-step guides for procedural questions or comprehensive reports for analytical questions

This enhanced research agent uses a recursive approach to explore topics in depth while adapting to different query types and search configurations. The `accumulatedResearch` object serves as a central data store that collects all queries, search results from multiple sources, and learnings throughout the research process. The intelligent agent selection ensures that the final output format matches the user's intent and query type.

## Docker Deployment

The Deep Research UI can be easily deployed using Docker containers. This is the recommended way to run the application in production.

### Quick Start with Docker

1. **Clone and navigate to the directory:**
   ```bash
   cd src/co_engineer/co_engineer/agent/deep_research_internal
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys
   ```

3. **Build and run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

4. **Access the application:**
   Open http://localhost:3000 in your browser

### Alternative Docker Methods

**Using the build script:**
```bash
# Build the image
./docker-build.sh build

# Run the container
./docker-build.sh run

# View logs
./docker-build.sh logs
```

**Using Docker commands directly:**
```bash
# Build
docker build -t deep-research-ui .

# Run
docker run -d --name deep-research-ui -p 3000:3000 --env-file .env deep-research-ui
```

For detailed Docker deployment instructions, see [DOCKER.md](DOCKER.md).

## Usage

### Web UI Usage

1. **Access the application** at http://localhost:3000
2. **Enter your research topic** in the input field
3. **Configure search settings:**
   - **Depth**: How many levels of follow-up research (1-5)
   - **Breadth**: How many search queries per level (1-5)
   - **Web Search Toggle**: Enable/disable web search functionality
     - **Enabled**: Uses both web search and knowledgebase search for comprehensive coverage
     - **Disabled**: Uses only knowledgebase search for internal documentation focus
4. **Click "Start Research"** and wait for results
5. **Review the automatically generated report** with intelligent format selection:
   - **How-to queries**: Receive detailed step-by-step procedural guides
   - **General queries**: Receive comprehensive analytical research reports
6. **Interact with the report** using built-in features:
   - Edit the report directly in the browser
   - Copy to clipboard
   - Download as Markdown file
   - Provide feedback for report refinement
   - Upload additional documents for context
   - Render Mermaid diagrams within the report

### Command Line Usage

```typescript
// Basic usage with intelligent agent selection
const research = await deepResearch(
  "How to create an OpenShift 4 cluster on bare metal servers using Redfish and agent-based installer"
);

// Automatically select the appropriate report format based on query type
const report = await selectAgentAndGenerateReport(research);

// Save the report to a file
fs.writeFileSync('report.md', report);

// Advanced usage with custom configuration
const research = await deepResearch(
  "How to configure Kubernetes networking?",
  3,        // depth - levels of recursive research
  4,        // breadth - number of search queries per level
  eventEmitter, // optional event emitter for progress tracking
  true      // enableWebSearch - true to include web search, false for knowledgebase only
);

// Manual agent selection (if you want to override automatic selection)
const stepByStepGuide = await generateStepByStepGuide(research);
const analyticalReport = await generateReport(research);

// Direct knowledgebase search (requires RAG API configuration)
import { searchKnowledgebase } from './main';

const kbResults = await searchKnowledgebase("How to configure Kubernetes networking?");
console.log(kbResults); // Array of SearchResult objects

// Check if a query is a how-to question
const isHowTo = await isHowToQuestion("How to install Docker on Ubuntu?");
console.log(isHowTo); // true
```

### Web UI

The system includes a web-based user interface for easier interaction. The UI provides:

- Form for entering research topics
- Real-time progress tracking
- Markdown rendering of the final report
- Copy and download options for reports

To use the web UI:

1. Navigate to the UI directory:
   ```bash
   cd ui
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build and start the server:
   ```bash
   npm run build && npm start
   ```

4. Open your browser to `http://localhost:3000`

For more details, see the [UI README](ui/README.md).

### Configuration Options

You can customize the research behavior with multiple parameters:

```typescript
// Full configuration options
const research = await deepResearch(
  "Your research topic here",
  3,              // depth - how many levels of follow-up questions to explore
  4,              // breadth - how many search queries to generate at each level
  eventEmitter,   // eventEmitter - for real-time progress tracking (optional)
  true,           // enableWebSearch - true for web+knowledgebase, false for knowledgebase only
  existingResearch // accumulatedResearch - for continuing previous research (optional)
);

// Minimal configuration (uses defaults)
const research = await deepResearch("Your research topic here");

// Knowledgebase-only research
const research = await deepResearch(
  "Internal documentation query",
  2,    // depth
  3,    // breadth
  null, // no event emitter
  false // disable web search, use only knowledgebase
);
```

## Environment Setup

1. Create a `.env` file with your API keys and configuration (you can copy from `.env.example`):
   ```
   # Required for web search
   EXA_API_KEY=your_exa_api_key_here

   # Optional: For knowledgebase search using RAG API
   RAG_API_BASE_URL=http://localhost:8000
   RAG_API_KEY=your_rag_api_key_here

   # For Google models
   GOOGLE_GENERATIVE_AI_API_KEY=<YOUR_API_KEY>

   # For OpenAI-compatible models
   OPENAI_API_BASE_URL=http://***********:4000  # Example URL for OpenAI-compatible API
   OPENAI_API_KEY=dummy                         # API key for OpenAI or compatible service

   # Model selection
   USE_OPENAI=true                              # Set to 'true' to use OpenAI-compatible models, 'false' for Google models

   # OpenAI-compatible model names
   OPENAI_MODEL_TOOL=QwQ-32B                    # Model for search queries and analysis
   OPENAI_MODEL_SUMMARY=DeepSeek-V3-0324        # Model for report generation
   ```

   > **Note**: The example configuration in `.env.example` uses OpenAI-compatible API endpoints with models like QwQ-32B and DeepSeek-V3-0324. You can replace these with actual OpenAI models like `gpt-4o` if using the official OpenAI API.

2. Install dependencies:
   ```bash
   npm install @ai-sdk/google @ai-sdk/openai ai dotenv zod exa-js
   ```

### Model Selection

The agent supports both Google Gemini and OpenAI-compatible models:

- When `USE_OPENAI=false`: Uses Google's `gemini-2.0-flash-001` model for all operations
- When `USE_OPENAI=true` (default in .env.example): Uses OpenAI-compatible models specified by:
  - `OPENAI_MODEL_TOOL`: Model used for search queries and analysis (default in .env.example: `QwQ-32B`)
  - `OPENAI_MODEL_SUMMARY`: Model used for report generation (default in .env.example: `DeepSeek-V3-0324`)

You can switch between model providers by changing the `USE_OPENAI` environment variable, and customize which models to use for different tasks without modifying the code. The agent is designed to work with both official OpenAI models and compatible alternatives through the same interface.

### Knowledgebase Search Integration

The agent now supports searching internal knowledgebases using RAG (Retrieval-Augmented Generation) APIs in addition to web search. This allows the agent to:

- Query internal documentation and knowledge repositories
- Access proprietary or private information not available on the web
- Combine internal knowledge with external web search results
- Use hybrid search modes (local, global, hybrid, naive, mix) for optimal retrieval

The knowledgebase search is implemented using the `/query` endpoint of a LightRAG-compatible API server. The agent automatically uses both web search and knowledgebase search tools during research, allowing the AI to choose the most appropriate source for each query.

**Configuration:**
- `RAG_API_BASE_URL`: Base URL of your RAG API server (default: `http://localhost:8000`)
- `RAG_API_KEY`: Optional API key for authentication

**Supported RAG API Features:**
- Multiple query modes (hybrid, local, global, naive, mix, bypass)
- Configurable token limits for context (configured to 128k tokens each):
  - `max_token_for_text_unit`: 131,072 tokens (128k)
  - `max_token_for_global_context`: 131,072 tokens (128k)
  - `max_token_for_local_context`: 131,072 tokens (128k)
- Custom user prompt to preserve detailed information without over-summarization
- Conversation history support
- Response type customization

3. Run the agent:
   ```bash
   npm run build && npm run start
   ```
   For example:
   ```bash
   # npm run build &&npm run start

    > ts@1.0.0 build
    > tsc


    > ts@1.0.0 start
    > node dist/main.js

    Searching the web for: Unattended Ubuntu installation bare metal server Redfish
    Found: https://foresterorg.github.io/
    Evaluation completed: relevant
    Processing search result: https://foresterorg.github.io/
    Searching the web for: Forester unattended Ubuntu installation Redfish SecureBoot
    Searching the web for: Redfish automated Ubuntu deployment bare metal
    Found: https://deploy.equinix.com/blog/redfish-and-the-future-of-bare-metal-server-automation/
    Evaluation completed: relevant
    Processing search result: https://deploy.equinix.com/blog/redfish-and-the-future-of-bare-metal-server-automation/
    Searching the web for: Redfish Ansible Terraform Ubuntu bare metal deployment
    Found: https://github.com/nickhardiman/summit_OD1226
    Evaluation completed: relevant
    Processing search result: https://github.com/nickhardiman/summit_OD1226
    Research completed!
    Generating report...
    Report generated! report.md
    ```

## Example Research Topics

The agent has been tested with topics such as:
- "How to create an Ubuntu autoinstall ISO"
- "How to install unattended Ubuntu to a bare metal server using Redfish"
- "How to create an OpenShift 4 cluster on bare metal servers using Redfish and agent-based installer"

## Example Reports

The [`example_reports`](example_reports/) directory contains several comprehensive research reports generated by the deep_research agent. These reports demonstrate the agent's ability to gather information, analyze it, and present it in a structured, detailed format.

### Available Reports

1. **[`report_ubuntu_autoinstall.md`](example_reports/report_ubuntu_autoinstall.md)**
   - **Topic:** Creating a customized Ubuntu autoinstall ISO
   - **Focus:** Detailed procedure for creating automated installation media, desktop environment customization, and troubleshooting
   - **Key Sections:** Core steps, detailed procedures, customizing desktop environments, addressing common issues
   - **Length:** ~250 lines, ~11,750 bytes

2. **[`report_unattended_ubuntu.md`](example_reports/report_unattended_ubuntu.md)**
   - **Topic:** Unattended Ubuntu installation on bare metal servers using Redfish
   - **Focus:** Redfish API usage, automation tools, and autoinstall configuration
   - **Key Sections:** Key components and technologies, unattended installation process, security considerations
   - **Length:** ~190 lines, ~11,680 bytes

3. **[`report_agent_based_installer.md`](example_reports/report_agent_based_installer.md)**
   - **Topic:** Unattended Ubuntu installation on bare metal servers via RedFish API
   - **Focus:** Technical implementation with code examples for RedFish automation
   - **Key Sections:** Step-by-step implementation, RedFish automation workflow, vendor-specific notes
   - **Length:** ~150 lines, ~5,350 bytes

4. **[`report_unattended_ubuntu_deepseek.md`](example_reports/report_unattended_ubuntu_deepseek.md)**
   - **Topic:** Unattended Ubuntu installation on bare metal via Redfish with static IP configuration
   - **Focus:** Comprehensive guide with NMState for network configuration and virtual media
   - **Key Sections:** Detailed steps, troubleshooting, alternatives and considerations
   - **Length:** ~280 lines, ~16,240 bytes

### Using the Example Reports

These reports serve as:
1. **Demonstrations** of the deep_research agent's capabilities
2. **Templates** for how to structure your own research requests
3. **Reference material** for the technical topics they cover

To generate similar reports, run the agent with a specific research topic as shown in the [main.ts](main.ts) file and the Usage section above. You can modify the research topic in the `deepResearch()` function call to generate reports on different subjects.

## Output

The agent intelligently generates different types of comprehensive Markdown reports based on the query type:

### Step-by-Step Guides (for How-to Questions)
- **Clear numbered steps** with detailed instructions for each phase
- **Prerequisites and requirements** listed at the beginning
- **Code snippets, commands, and configuration examples** where relevant
- **Troubleshooting tips** for common issues that might arise
- **Verification steps** to ensure successful completion
- **Important warnings and cautions** highlighted in bold
- **Organized structure** that's easy to follow sequentially

### Research Reports (for General Questions)
- **Detailed findings** from comprehensive research across multiple sources
- **Organized sections** with key information and analysis
- **Citations to sources** from both web and knowledgebase searches
- **Technical details** appropriate for expert users
- **Potential solutions and approaches** with comparative analysis
- **Contrarian ideas and alternative perspectives** for balanced coverage
- **Expert-level insights** with high levels of detail and accuracy

### Common Features (Both Report Types)
- **Markdown formatting** for excellent readability
- **Source attribution** from web and knowledgebase searches
- **Comprehensive coverage** through recursive research methodology
- **Expert-level detail** without oversimplification
- **Structured organization** for easy navigation and reference

## System Requirements

- Node.js 16+
- TypeScript 4.5+
- Valid Exa API key
- Internet connection for web searches

## Limitations

- The quality of research depends on the availability of relevant web content
- The agent is designed for technical research and may not perform as well for subjective topics
- Web search results may occasionally contain outdated information

## Future Improvements

- Integration with additional search providers
- Support for PDF and academic paper analysis
- Improved source evaluation and fact-checking
- Interactive mode for real-time research guidance

## Code Structure

The deep_research system is composed of several specialized agents, each with a specific responsibility in the enhanced multi-agent architecture:

| Agent | Description |
|----------|-------------|
| `deepResearch` | Core orchestration agent that manages the recursive research process with configurable search options |
| `generateSearchQueries` | Query formulation agent that creates targeted search queries based on the research topic |
| `searchWeb` | Web search agent that performs searches using the Exa library (conditionally enabled) |
| `searchKnowledgebase` | Knowledgebase search agent that queries internal RAG systems for documentation and knowledge |
| `searchAndProcess` | Unified search & evaluation agent that manages both web and knowledgebase searches with integrated relevance assessment |
| `generateLearnings` | Insight extraction agent that identifies key learnings and generates follow-up questions |
| `selectAgentAndGenerateReport` | Agent selection coordinator that intelligently chooses between different report generation strategies |
| `isHowToQuestion` | Query analysis agent that determines if the research query requires procedural instructions |
| `generateStepByStepGuide` | Step-by-step guide agent that generates detailed procedural guides for how-to questions |
| `generateReport` | Research report agent that synthesizes all collected data into comprehensive analytical reports |

### Enhanced Agent Features

**Intelligent Agent Selection**: The system now automatically determines the most appropriate report format based on the research query type:
- **How-to queries** → Step-by-step procedural guides
- **General queries** → Comprehensive research reports

**Adaptive Search Capabilities**:
- **Knowledgebase search**: Always enabled for internal documentation access
- **Web search**: Conditionally enabled based on user preferences or system configuration
- **Integrated evaluation**: Real-time relevance assessment during search execution

**Event-Driven Architecture**: Support for real-time progress tracking through event emission for UI integration.

The agent system uses a shared memory structure called `accumulatedResearch` to store and exchange research data between agents:

```typescript
type Research = {
  query: string | undefined       // The original research topic
  queries: string[]               // Generated search queries from the query formulation agent
  searchResults: SearchResult[]   // Relevant results from both web and knowledgebase searches
  learnings: Learning[]           // Insights and questions from the extraction agent
  completedQueries: string[]      // Tracking of processed queries
}
```

This shared memory approach allows the agents to maintain context throughout the recursive research process, build upon each other's work, and collectively generate the most appropriate final report format based on the query type and available information sources.
