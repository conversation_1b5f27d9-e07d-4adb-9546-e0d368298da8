import 'dotenv/config';

// Simple test to verify the evaluation logic works
console.log('=== Testing Evaluation Fix ===');

// Mock the search result structure
type SearchResult = {
  title: string;
  url: string;
  content: string;
};

// Test data
const mockSearchResults: SearchResult[] = [
  {
    title: 'Test Result 1',
    url: 'http://example.com/1',
    content: 'This is test content 1'
  },
  {
    title: 'Test Result 2', 
    url: 'http://example.com/2',
    content: 'This is test content 2'
  }
];

// Simulate the new evaluation logic
const testEvaluationLogic = async () => {
  console.log('Testing new evaluation logic...');
  
  const finalSearchResults: SearchResult[] = [];
  const accumulatedSources: SearchResult[] = [];
  
  // Simulate what happens in the search tools now
  for (const result of mockSearchResults) {
    console.log(`Processing result: ${result.url}`);
    
    // Simulate evaluation (without actual AI call)
    const evaluation = 'relevant'; // Mock evaluation result
    
    if (evaluation === 'relevant') {
      finalSearchResults.push(result);
      console.log(`✅ Added relevant result: ${result.url}`);
    } else {
      console.log(`❌ Skipped irrelevant result: ${result.url}`);
    }
  }
  
  console.log(`\nFinal results: ${finalSearchResults.length} relevant results found`);
  console.log('Results:', finalSearchResults.map(r => r.url));
  
  return finalSearchResults;
};

// Run the test
testEvaluationLogic().then(results => {
  console.log('\n=== Test Completed ===');
  console.log(`Success: Found ${results.length} results`);
  console.log('The new logic should now work correctly in the actual system!');
}).catch(error => {
  console.error('Test failed:', error);
});
