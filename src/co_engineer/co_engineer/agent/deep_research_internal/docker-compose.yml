version: '3.8'

services:
  deep-research-ui:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deep-research-ui
    ports:
      - "3000:3000"
    environment:
      # AI Model Configuration
      - USE_OPENAI=true
      - OPENAI_API_KEY=${OPENAI_API_KEY:-your-openai-api-key}
      - OPENAI_API_BASE_URL=${OPENAI_API_BASE_URL:-http://***********:4000}
      - OPENAI_MODEL_TOOL=${OPENAI_MODEL_TOOL:-QwQ-32B}
      - OPENAI_MODEL_SUMMARY=${OPENAI_MODEL_SUMMARY:-DeepSeek-V3-0324}
      
      # Google AI Configuration (if USE_OPENAI=false)
      - GOOGLE_GENERATIVE_AI_API_KEY=${GOOGLE_GENERATIVE_AI_API_KEY:-your-google-api-key}
      
      # Search Configuration
      - EXA_API_KEY=${EXA_API_KEY:-your-exa-api-key}
      
      # RAG Configuration
      - RAG_API_BASE_URL=${RAG_API_BASE_URL:-http://localhost:8000}
      - RAG_API_KEY=${RAG_API_KEY:-your-rag-api-key}
      
      # Server Configuration
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
    volumes:
      # Optional: Mount a volume for persistent logs
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - deep-research-network

networks:
  deep-research-network:
    driver: bridge
