document.addEventListener('DOMContentLoaded', () => {
    // Connect to Socket.io server
    const socket = io();

    // DOM elements
    const researchForm = document.getElementById('research-form');
    const topicInput = document.getElementById('topic');
    const depthSelect = document.getElementById('depth');
    const breadthSelect = document.getElementById('breadth');
    const enableWebSearchToggle = document.getElementById('enable-web-search');
    const startResearchBtn = document.getElementById('start-research');
    const progressContainer = document.getElementById('progress-container');
    const reportContainer = document.getElementById('report-container');
    const editorContainer = document.getElementById('editor-container');
    const reportEditor = document.getElementById('report-editor');
    const editReportBtn = document.getElementById('edit-report');
    const doneEditingBtn = document.getElementById('done-editing');
    const copyReportBtn = document.getElementById('copy-report');
    const downloadReportBtn = document.getElementById('download-report');
    const feedbackSection = document.getElementById('feedback-section');
    const feedbackForm = document.getElementById('feedback-form');
    const feedbackInput = document.getElementById('feedback-input');

    // Current research state
    let currentReport = '';
    let currentReportFilename = '';
    let isResearching = false;
    let currentResearch = null; // Store the current research data
    let isProcessingDocument = false; // Flag to track document processing

    // Initialize marked.js with highlight.js and mermaid support
    function configureMarked() {
        marked.setOptions({
            highlight: function(code, lang) {
                // Special handling for mermaid diagrams
                if (lang === 'mermaid') {
                    return `<div class="mermaid">${code}</div>`;
                }

                // Check if highlight.js is available for other code blocks
                if (typeof hljs !== 'undefined') {
                    try {
                        if (lang && hljs.getLanguage(lang)) {
                            return hljs.highlight(code, { language: lang }).value;
                        }
                        return hljs.highlightAuto(code).value;
                    } catch (e) {
                        console.warn('Error using highlight.js:', e);
                        return code; // Fallback to plain code
                    }
                }
                // Fallback if highlight.js is not available
                return code;
            },
            breaks: true,
            gfm: true,
            headerIds: true,
            mangle: false
        });
        console.log('Marked.js configured successfully with Mermaid support');
    }

    // Initialize Mermaid
    function initMermaid() {
        if (typeof mermaid !== 'undefined') {
            try {
                mermaid.initialize({
                    startOnLoad: false,
                    theme: 'default',
                    securityLevel: 'loose'
                });
                console.log('Mermaid initialized successfully');
            } catch (e) {
                console.warn('Error initializing Mermaid:', e);
            }
        } else {
            console.warn('Mermaid library not loaded');
        }
    }

    // Initial configuration
    configureMarked();
    initMermaid();

    // Reconfigure if highlight.js loads later
    window.addEventListener('load', function() {
        // Wait a bit to ensure any async scripts have loaded
        setTimeout(function() {
            if (typeof hljs !== 'undefined') {
                console.log('highlight.js detected after page load, reconfiguring marked');
                configureMarked();
            }
            if (typeof mermaid !== 'undefined') {
                console.log('mermaid detected after page load, initializing');
                initMermaid();
            }
        }, 1000);
    });

    // Handle research form submission
    researchForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const topic = topicInput.value.trim();
        if (!topic) {
            alert('Please enter a research topic');
            return;
        }

        if (isResearching) {
            if (!confirm('Research is already in progress. Start a new one?')) {
                return;
            }
        }

        // Hide feedback section when starting new research
        feedbackSection.style.display = 'none';

        // Get web search toggle state
        const enableWebSearch = enableWebSearchToggle.checked;

        startResearch(topic, parseInt(depthSelect.value), parseInt(breadthSelect.value), enableWebSearch);
    });

    // Handle edit button click
    editReportBtn.addEventListener('click', () => {
        // Switch to edit mode
        toggleEditMode(true);
    });

    // Handle done editing button click
    doneEditingBtn.addEventListener('click', () => {
        // Apply changes and switch back to preview mode
        applyEdits();
    });

    // Handle add document button click
    const addDocBtn = document.getElementById('add-doc-btn');
    const docUpload = document.getElementById('doc-upload');

    addDocBtn.addEventListener('click', () => {
        // Trigger file input click
        docUpload.click();
    });

    // Handle file selection
    docUpload.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (!file) return;

        // Check if it's a markdown file
        if (!file.name.toLowerCase().endsWith('.md')) {
            alert('Please select a Markdown (.md) file');
            docUpload.value = ''; // Clear the file input
            return;
        }

        // Check if we have research data
        if (!currentResearch) {
            alert('Please start a research first before adding documents');
            docUpload.value = ''; // Clear the file input
            return;
        }

        // Disable the button while processing
        addDocBtn.disabled = true;
        // Save the original button content
        const originalBtnContent = addDocBtn.innerHTML;
        addDocBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';

        // Read the file
        const reader = new FileReader();
        reader.onload = (event) => {
            const documentContent = event.target.result;

            // Set processing flag
            isProcessingDocument = true;

            // Send the document to the server
            socket.emit('processDocument', {
                documentContent,
                documentName: file.name,
                research: currentResearch
            });

            addProgressItem('info', 'Document Added', `Processing document: ${file.name}`);
        };

        reader.onerror = () => {
            alert('Error reading the file');
            addDocBtn.disabled = false;
            addDocBtn.innerHTML = originalBtnContent;
            docUpload.value = ''; // Clear the file input
        };

        reader.readAsText(file);
    });

    // Handle feedback form submission
    feedbackForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const feedback = feedbackInput.value.trim();
        if (!feedback) {
            alert('Please enter your feedback');
            return;
        }

        // Disable the submit button while processing
        const submitBtn = document.getElementById('submit-feedback');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Applying...';

        // Send feedback to server
        socket.emit('applyFeedback', {
            feedback,
            research: currentResearch
        });

        addProgressItem('info', 'Feedback Submitted', truncateText(feedback, 100));
    });

    // Copy report button
    copyReportBtn.addEventListener('click', () => {
        if (currentReport) {
            navigator.clipboard.writeText(currentReport)
                .then(() => {
                    const originalText = copyReportBtn.textContent;
                    copyReportBtn.textContent = 'Copied!';
                    setTimeout(() => {
                        copyReportBtn.textContent = originalText;
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy: ', err);
                    alert('Failed to copy report to clipboard');
                });
        }
    });

    // Download report button
    downloadReportBtn.addEventListener('click', () => {
        if (currentReport) {
            const blob = new Blob([currentReport], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            // Use the filename provided by the server, or generate one if not available
            a.download = currentReportFilename || `deep_research_report_${Date.now()}.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    });

    // Toggle between edit mode and preview mode
    function toggleEditMode(editMode) {
        if (editMode) {
            // Switch to edit mode
            reportContainer.style.display = 'none';
            editorContainer.style.display = 'block';

            // Populate editor with current report content
            reportEditor.value = currentReport;

            // Focus the editor
            reportEditor.focus();
        } else {
            // Switch to preview mode
            reportContainer.style.display = 'block';
            editorContainer.style.display = 'none';
        }
    }

    // Apply edits and update the report
    function applyEdits() {
        // Get the edited content
        const editedContent = reportEditor.value;

        // Update the current report
        currentReport = editedContent;

        // Render the updated report
        try {
            const parsedMarkdown = marked.parse(editedContent);
            reportContainer.innerHTML = `<div class="markdown-content">${parsedMarkdown}</div>`;

            // Render Mermaid diagrams if available
            if (typeof mermaid !== 'undefined') {
                try {
                    mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                } catch (e) {
                    console.warn('Error rendering Mermaid diagrams:', e);
                }
            }

            // Add a progress item to indicate the report was edited
            addProgressItem('info', 'Report Edited', 'Report content was manually edited');

        } catch (error) {
            console.error('Error rendering edited markdown:', error);
            reportContainer.innerHTML = `
                <div class="alert alert-warning">
                    <h4>Markdown Rendering Error</h4>
                    <p>There was an error rendering the edited report.</p>
                    <pre class="mt-3">${editedContent.substring(0, 500)}...</pre>
                </div>
            `;
        }

        // Switch back to preview mode
        toggleEditMode(false);
    }

    // Start research function
    function startResearch(topic, depth, breadth, enableWebSearch = true) {
        // Reset UI
        isResearching = true;
        currentReport = '';
        currentReportFilename = '';
        progressContainer.innerHTML = '<div class="progress-status">Starting research...</div>';
        reportContainer.innerHTML = `
            <div class="placeholder-message">
                <div class="text-center">
                    <div class="loading"></div>
                    <h4 class="mt-3">Researching...</h4>
                    <p class="text-muted">This may take a few minutes depending on the depth and breadth settings.</p>
                </div>
            </div>
        `;
        copyReportBtn.disabled = true;
        downloadReportBtn.disabled = true;
        editReportBtn.disabled = true;
        startResearchBtn.disabled = true;

        // Make sure we're in preview mode
        toggleEditMode(false);

        // Send research request to server
        socket.emit('startResearch', { topic, depth, breadth, enableWebSearch });
    }

    // Socket.io event handlers
    socket.on('connect', () => {
        console.log('Connected to server');
    });

    socket.on('researchStarted', (data) => {
        addProgressItem('info', 'Research Started', `Topic: ${data.topic}`);
    });

    socket.on('searchStarted', (data) => {
        addProgressItem('search', 'Searching', data.query);
    });

    socket.on('searchCompleted', (data) => {
        // Create badge based on relevance
        const badge = data.relevant ?
            '<span class="badge bg-success">Relevant</span>' :
            '<span class="badge bg-secondary">Not relevant</span>';

        addProgressItem('result', 'Search Result',
            `${badge} <a href="${data.url}" target="_blank">${truncateUrl(data.url)}</a>`);
    });

    socket.on('learningExtracted', (data) => {
        addProgressItem('learning', 'Learning Extracted',
            `${truncateText(data.learning, 100)}`);
    });

    socket.on('generatingReport', () => {
        addProgressItem('info', 'Generating Report', 'Synthesizing research findings...');
    });

    socket.on('researchCompleted', (data) => {
        console.log('Received researchCompleted event:', {
            reportLength: data.report ? data.report.length : 0,
            filename: data.filename
        });

        isResearching = false;
        startResearchBtn.disabled = false;
        currentReport = data.report;
        currentReportFilename = data.filename || `deep_research_report_${Date.now()}.md`;

        // Store the research data for potential feedback
        currentResearch = data.research || null;

        // Show the feedback section now that research is complete
        feedbackSection.style.display = 'block';

        // Render the report
        console.log('Rendering report to DOM...');

        // First, clear the "Researching..." message
        reportContainer.innerHTML = '';

        // Check if we have a report to render
        if (!data.report) {
            console.error('No report data received');
            reportContainer.innerHTML = `
                <div class="alert alert-warning">
                    <h4>Empty Report</h4>
                    <p>No report data was received from the server.</p>
                </div>
            `;
        } else {
            try {
                console.log('Report data received, length:', data.report.length);
                console.log('Sample of report:', data.report.substring(0, 100) + '...');

                // Parse the markdown
                const parsedMarkdown = marked.parse(data.report);
                console.log('Markdown parsed successfully, length:', parsedMarkdown.length);

                // Render to DOM
                reportContainer.innerHTML = `<div class="markdown-content">${parsedMarkdown}</div>`;
                console.log('Report rendered to DOM');

                // Force a reflow to ensure the content is displayed
                void reportContainer.offsetHeight;

                // Render Mermaid diagrams if available
                if (typeof mermaid !== 'undefined') {
                    try {
                        console.log('Rendering Mermaid diagrams...');
                        mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                        console.log('Mermaid diagrams rendered successfully');
                    } catch (e) {
                        console.warn('Error rendering Mermaid diagrams:', e);
                    }
                }
            } catch (error) {
                console.error('Error rendering markdown:', error);
                reportContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <h4>Markdown Rendering Error</h4>
                        <p>There was an error rendering the report. You can still download the raw markdown.</p>
                        <pre class="mt-3">${data.report.substring(0, 500)}...</pre>
                    </div>
                `;
            }
        }

        // Enable report actions
        copyReportBtn.disabled = false;
        downloadReportBtn.disabled = false;
        editReportBtn.disabled = false;

        addProgressItem('success', 'Research Completed', 'Report generated successfully');

        // Scroll to top of report
        reportContainer.scrollTop = 0;
    });

    socket.on('researchError', (data) => {
        isResearching = false;
        isProcessingDocument = false;
        startResearchBtn.disabled = false;
        editReportBtn.disabled = true;

        reportContainer.innerHTML = `
            <div class="alert alert-danger">
                <h4>Error</h4>
                <p>${data.error}</p>
            </div>
        `;

        addProgressItem('error', 'Research Error', data.error);

        // Hide feedback section on error
        feedbackSection.style.display = 'none';

        // Re-enable the feedback submit button if it was disabled
        const submitBtn = document.getElementById('submit-feedback');
        if (submitBtn.disabled) {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Apply Feedback';
        }

        // Re-enable the add document button if it was disabled
        const addDocBtn = document.getElementById('add-doc-btn');
        if (addDocBtn.disabled) {
            addDocBtn.disabled = false;
            // Restore the original button content with the icon
            addDocBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-text me-1" viewBox="0 0 16 16">
                    <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
                    <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                </svg>
                Add Doc
            `;
            docUpload.value = ''; // Clear the file input
        }
    });

    // Handle feedback response from server
    socket.on('feedbackApplied', (data) => {
        // Update the report with the adjusted content
        currentReport = data.report;

        // Render the updated report
        try {
            const parsedMarkdown = marked.parse(data.report);
            reportContainer.innerHTML = `<div class="markdown-content">${parsedMarkdown}</div>`;

            // Render Mermaid diagrams if available
            if (typeof mermaid !== 'undefined') {
                try {
                    mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                } catch (e) {
                    console.warn('Error rendering Mermaid diagrams:', e);
                }
            }

            addProgressItem('success', 'Feedback Applied', 'Research results adjusted based on feedback');

            // Reset the feedback form
            feedbackInput.value = '';

            // Re-enable the submit button
            const submitBtn = document.getElementById('submit-feedback');
            submitBtn.disabled = false;
            submitBtn.textContent = 'Apply Feedback';

            // Make sure edit button is enabled
            editReportBtn.disabled = false;

            // Add visual feedback
            feedbackSection.classList.add('active');
            setTimeout(() => {
                feedbackSection.classList.remove('active');
            }, 2000);

        } catch (error) {
            console.error('Error rendering updated markdown:', error);
            reportContainer.innerHTML = `
                <div class="alert alert-warning">
                    <h4>Markdown Rendering Error</h4>
                    <p>There was an error rendering the updated report.</p>
                    <pre class="mt-3">${data.report.substring(0, 500)}...</pre>
                </div>
            `;
        }
    });

    // Handle document processed response from server
    socket.on('documentProcessed', (data) => {
        // Update the report with the adjusted content
        currentReport = data.report;

        // Reset processing flag
        isProcessingDocument = false;

        // Render the updated report
        try {
            const parsedMarkdown = marked.parse(data.report);
            reportContainer.innerHTML = `<div class="markdown-content">${parsedMarkdown}</div>`;

            // Render Mermaid diagrams if available
            if (typeof mermaid !== 'undefined') {
                try {
                    mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                } catch (e) {
                    console.warn('Error rendering Mermaid diagrams:', e);
                }
            }

            addProgressItem('success', 'Document Processed', `Document "${data.documentName}" incorporated into research`);

            // Reset the file input
            docUpload.value = '';

            // Re-enable the add document button
            const addDocBtn = document.getElementById('add-doc-btn');
            addDocBtn.disabled = false;
            // Restore the original button content with the icon
            addDocBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-text me-1" viewBox="0 0 16 16">
                    <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
                    <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                </svg>
                Add Doc
            `;

            // Make sure edit button is enabled
            editReportBtn.disabled = false;

        } catch (error) {
            console.error('Error rendering updated markdown:', error);
            reportContainer.innerHTML = `
                <div class="alert alert-warning">
                    <h4>Markdown Rendering Error</h4>
                    <p>There was an error rendering the updated report.</p>
                    <pre class="mt-3">${data.report.substring(0, 500)}...</pre>
                </div>
            `;

            // Reset the file input and re-enable the button
            docUpload.value = '';
            const addDocBtn = document.getElementById('add-doc-btn');
            addDocBtn.disabled = false;
            // Restore the original button content with the icon
            addDocBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-text me-1" viewBox="0 0 16 16">
                    <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
                    <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                </svg>
                Add Doc
            `;
        }
    });

    // Helper function to add progress item
    function addProgressItem(type, title, content) {
        const item = document.createElement('div');
        item.className = `progress-item ${type}`;
        item.innerHTML = `
            <h5>${title}</h5>
            <p>${content}</p>
        `;

        // Add timestamp
        const timestamp = document.createElement('small');
        timestamp.className = 'text-muted d-block mt-1';
        timestamp.textContent = new Date().toLocaleTimeString();
        item.appendChild(timestamp);

        // Remove "Waiting to start..." message if it exists
        const waitingMsg = progressContainer.querySelector('.progress-status');
        if (waitingMsg) {
            progressContainer.removeChild(waitingMsg);
        }

        progressContainer.appendChild(item);
        progressContainer.scrollTop = progressContainer.scrollHeight;
    }

    // Helper function to truncate URL
    function truncateUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname.length > 20 ?
                urlObj.pathname.substring(0, 20) + '...' : urlObj.pathname);
        } catch (e) {
            return url.length > 40 ? url.substring(0, 40) + '...' : url;
        }
    }

    // Helper function to truncate text
    function truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
});
