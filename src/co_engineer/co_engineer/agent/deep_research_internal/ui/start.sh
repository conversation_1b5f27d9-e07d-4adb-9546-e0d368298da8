#!/bin/bash

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js to run this application."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "npm is not installed. Please install npm to run this application."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Install ts-node globally if not already installed
if ! command -v ts-node &> /dev/null; then
    echo "Installing ts-node globally..."
    npm install -g ts-node
fi

# Create reports directory if it doesn't exist
mkdir -p public/reports

# Start the server using ts-node
echo "Starting Deep Research UI server..."
npm start
