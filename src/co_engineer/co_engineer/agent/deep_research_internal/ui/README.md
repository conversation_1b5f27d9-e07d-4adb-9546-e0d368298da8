# Deep Research UI

A web-based user interface for the Deep Research Multi-Agent System. This UI allows users to:

1. Enter research topics
2. Configure research depth and breadth
3. View real-time research progress
4. See the final research report in Markdown format
5. Copy or download the generated report

## Features

- **Interactive Web Interface**: Clean, responsive design using Bootstrap
- **Real-time Updates**: Socket.io for live progress updates
- **Markdown Rendering**: Beautifully formatted research reports
- **Syntax Highlighting**: Code blocks are highlighted for readability
- **Progress Tracking**: See what the agent is researching in real-time

## Screenshots

![Deep Research UI](./assets/image.png)

## Installation

1. Make sure you have Node.js installed (v14+ recommended)

2. Navigate to the UI directory:
   ```bash
   cd src/co_engineer/co_engineer/agent/deep_research/ui
   ```

3. Run the start script which will handle installation, building, and starting the server:
   ```bash
   ./start.sh
   ```

## Manual Setup (Alternative)

If you prefer to run the steps manually:

1. Install dependencies:
   ```bash
   npm install
   ```

2. Install ts-node globally (if not already installed):
   ```bash
   npm install -g ts-node
   ```

3. Create the reports directory:
   ```bash
   mkdir -p public/reports
   ```

4. Start the server directly with ts-node:
   ```bash
   ts-node server.ts
   ```

   Or use npm script:
   ```bash
   npm start
   ```

## Usage

1. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

3. Enter a research topic, adjust depth and breadth settings if needed, and click "Start Research"

4. Watch as the agent performs research in real-time

5. When complete, view, copy, or download the generated report

## Configuration

The UI uses the same environment variables as the main Deep Research agent. Make sure your `.env` file is properly configured with API keys for:

- Exa Search API
- Google Generative AI or OpenAI (depending on your configuration)

## Development

For development with hot-reloading:

```bash
npm run dev
```

## How It Works

The UI is built with:

- **Express.js**: Backend server
- **Socket.io**: Real-time communication
- **Bootstrap**: Responsive UI components
- **Marked.js**: Markdown rendering
- **Highlight.js**: Syntax highlighting

The server communicates with the Deep Research agent and streams progress updates to the client in real-time.
