# Multi-stage build for Deep Research UI
FROM node:24.3.0-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files for both main app and UI
COPY package*.json ./
COPY ui/package*.json ./ui/

# Install dependencies for main app
RUN npm ci --only=production

# Install dependencies for UI
WORKDIR /app/ui
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY ui/package*.json ./ui/

# Install all dependencies (including dev dependencies)
RUN npm ci
WORKDIR /app/ui
RUN npm ci

# Copy source code
WORKDIR /app
COPY . .

# Build the main TypeScript application
RUN npm run build

# Build the UI TypeScript application
WORKDIR /app/ui
RUN npm run build

# Production image, copy all the files and run the application
FROM base AS runner
WORKDIR /app

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nodejs

# Copy built application
COPY --from=builder --chown=nodejs:nodejs /app/dist ./dist
COPY --from=builder --chown=nodejs:nodejs /app/ui/dist ./ui/dist
COPY --from=builder --chown=nodejs:nodejs /app/ui/public ./ui/public

# Copy package files and install production dependencies
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --from=deps --chown=nodejs:nodejs /app/ui/node_modules ./ui/node_modules
COPY --chown=nodejs:nodejs package*.json ./
COPY --chown=nodejs:nodejs ui/package*.json ./ui/

# Copy TypeScript config files (needed for ts-node in production)
COPY --chown=nodejs:nodejs tsconfig.json ./
COPY --chown=nodejs:nodejs ui/tsconfig.json ./ui/

# Copy source files (needed because UI imports from parent directory)
COPY --chown=nodejs:nodejs main.ts ./
COPY --chown=nodejs:nodejs ui/server.ts ./ui/

USER nodejs

# Expose the port
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV HOST=0.0.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
WORKDIR /app/ui
CMD ["npm", "start"]
