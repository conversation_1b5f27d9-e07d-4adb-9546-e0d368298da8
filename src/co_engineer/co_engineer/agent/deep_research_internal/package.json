{"name": "ts", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node main.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@ai-sdk/google": "^1.2.13", "@ai-sdk/openai": "^1.3.18", "@google/generative-ai": "^0.24.0", "@types/node": "^20.17.30", "ai": "^4.3.9", "dotenv": "^16.5.0", "exa-js": "^1.6.13", "https-proxy-agent": "^7.0.6", "socks-proxy-agent": "^8.0.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}