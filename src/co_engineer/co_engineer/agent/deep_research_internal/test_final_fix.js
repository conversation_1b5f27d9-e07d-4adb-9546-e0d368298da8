// Simple JavaScript test to verify the fix works
console.log('=== Testing Final Web Search Toggle Fix ===');

// Simulate the tools creation logic from main.ts
function createToolsForTesting(enableWebSearch) {
  console.log(`\nTesting with enableWebSearch: ${enableWebSearch}`);
  
  // This simulates the IIFE (Immediately Invoked Function Expression) from main.ts
  const tools = (() => {
    const baseTools = {
      searchKnowledgebase: {
        name: 'Search the internal knowledgebase',
        execute: () => 'knowledgebase search executed'
      },
      checkStatus: {
        name: 'Check status',
        execute: () => 'status check executed'
      },
    };

    // Conditionally add web search tool
    if (enableWebSearch) {
      baseTools.searchWeb = {
        name: 'Search the web',
        execute: () => 'web search executed'
      };
    }

    return baseTools;
  })();
  
  const toolNames = Object.keys(tools);
  console.log(`Available tools: [${toolNames.join(', ')}]`);
  console.log(`Total tools: ${toolNames.length}`);
  console.log(`Has searchWeb: ${tools.hasOwnProperty('searchWeb')}`);
  console.log(`Has searchKnowledgebase: ${tools.hasOwnProperty('searchKnowledgebase')}`);
  
  return tools;
}

// Test both scenarios
console.log('\n--- Scenario 1: Web Search ENABLED ---');
const toolsEnabled = createToolsForTesting(true);

console.log('\n--- Scenario 2: Web Search DISABLED ---');
const toolsDisabled = createToolsForTesting(false);

// Verify the fix
console.log('\n=== Verification ===');
const enabledHasWeb = toolsEnabled.hasOwnProperty('searchWeb');
const disabledHasWeb = toolsDisabled.hasOwnProperty('searchWeb');

if (enabledHasWeb && !disabledHasWeb) {
  console.log('✅ SUCCESS: Web search toggle works correctly!');
  console.log('✅ When enabled: searchWeb tool is available');
  console.log('✅ When disabled: searchWeb tool is NOT available');
  console.log('✅ searchKnowledgebase is always available');
} else {
  console.log('❌ FAILED: Web search toggle is not working correctly');
  console.log(`   - Enabled has searchWeb: ${enabledHasWeb}`);
  console.log(`   - Disabled has searchWeb: ${disabledHasWeb}`);
}

console.log('\nThe fix should now prevent web search when the toggle is disabled!');
