#!/bin/bash

# --- Configuration ---
BACKEND="vllm"
MODEL_NAME="Qwen/Qwen3-30B-A3B" # Your LLM model name
BASE_URL="http://127.0.0.1:8000" # URL of your LLM inference service
NUM_PROMPTS=2048 # Total number of prompts for the benchmark run
DATASET_NAME="sonnet" # Dataset name, often a placeholder when using --sonnet-input-len
DATASET_PATH="sonnet.txt" # Path to a dummy dataset file, often a placeholder

# Define tasks with their input and output lengths
declare -A tasks
tasks["Simple Q&A"]="100 500"
tasks["Reasoning"]="100 2000"
tasks["RAG"]="5000 1000"
tasks["Agentic (cline)"]="18000 300"
tasks["Deep Research"]="20000 3000"

# Concurrency levels to test
CONCURRENCIES=(5 10 20 30)

# --- Script Logic ---

echo "Starting LLM Inference Service Benchmarking..."
echo "------------------------------------------------"

for task_name in "${!tasks[@]}"; do
    read -r input_len output_len <<< "${tasks[$task_name]}"

    echo "--- Benchmarking Task: $task_name ---"
    echo "    Input Length: $input_len, Output Length: $output_len"

    for concurrency in "${CONCURRENCIES[@]}"; do
        echo "    Running with Concurrency: $concurrency"
        
        # Construct the benchmark command
        CMD="python3 benchmark_serving.py \
            --backend $BACKEND \
            --model $MODEL_NAME \
            --dataset-name $DATASET_NAME \
            --dataset-path $DATASET_PATH \
            --num-prompts $NUM_PROMPTS \
            --sonnet-input-len $input_len \
            --sonnet-output-len $output_len \
            --base-url $BASE_URL \
            --max-concurrency $concurrency"
        
        echo "    Executing command: $CMD"
        
        # Execute the command
        eval $CMD
        
        # Optional: Add a small delay between runs if needed to allow server to cool down
        # sleep 5 
        echo "" # Add a newline for better readability between runs
    done
    echo "------------------------------------------------"
done

echo "Benchmarking completed."